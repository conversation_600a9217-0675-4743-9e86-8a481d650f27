//orginal

import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  Image,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  View,
  KeyboardAvoidingView
} from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  useCart,
  useCartActions,
  currencyHelper,
  useProductWishList,
  useUser,
  useCurrentUser, useWishlistProducts
} from '@appmaker-xyz/shopify';
import { useAppStorage } from '@appmaker-xyz/core';
import CartLineItemCard from './CartLineItemCard';
import { runDataSource } from '@appmaker-xyz/core';
import Svg, { Circle, G, Path, Rect } from 'react-native-svg';
import LinearGradient from 'react-native-linear-gradient';
import { handleAction } from '@appmaker-xyz/react-native';
import { AppTouchable } from '@appmaker-xyz/ui';
import CartSummaryTable from './CartSummaryTable';
import Ripple from 'react-native-material-ripple';
import { Confirmations } from './Confirmations';
import {
  fonts,
  getVerticalPadding,
  heightPixel,
  widthPixel,
} from '../../styles';
import ProductModal from '../common/ProductModal';
import Icon from 'react-native-vector-icons/AntDesign';
import Card from '../Search/Card';
import { getIdFromGID, updateWebWishlist } from '../../utils/Helper';
import ToolbarIcon from '../common/ToolbarIcon';
import CartIcon from '../../assets/icons/CartIcon';
import { useNavigation } from '@react-navigation/native';
import WishlistIcon from '../../assets/icons/WishlistIcon';
import GiftCardIcon from '../../assets/images/GiftCardIcon.svg';
import { getUser } from '@appmaker-xyz/core';
import { Buffer } from 'buffer';
const { height, width } = Dimensions.get('window');

const dataSource = {
  attributes: {},
  source: 'shopify',
};

const CustomCartBlock = (props) => {
  // console.log("PROPS",props)
  const insets = useSafeAreaInsets();
  const { manageCart, cartActionLoading } = useCartActions({});
  const scrollViewRef = useRef(null);
  const { isLoggedin } = useUser();

  const navigation = useNavigation();
  useEffect(() => {
    customAttributesAction();
    fetchAppliedGiftItems();
    fetchRedeemedAmountSum();
    fetchRedeemedGiftItems();
  }, [])

  const userRequestMetafields = [
    {
      namespace: 'jockey_balance',
      key: 'customer_balance',
    },
  ];
  const { data } = useCurrentUser({ metafields: userRequestMetafields });
  const customerId = getIdFromGID(data?.customer?.id);

  const [recommendation, setRecommendation] = useState([]);
  const [loading, setLoading] = useState(false);

  const { checkout } = useAppStorage();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showWishlistConfirmation, setShowWishlistConfirmation] =
    useState(false);

  const { products, isLoading } = useWishlistProducts(props);
  const [selectedProductData, setSelectedProductData] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showGiftModal, setShowGiftModal] = useState(false);
  const [showAppliedVoucherModal, setShowAppliedVoucherModal] = useState(false);
  const [activeTab, setActiveTab] = useState('Gift Card');
  const [giftCardNumber, setGiftCardNumber] = useState('');
  const [giftCardPin, setGiftCardPin] = useState('');
  const [giftVoucherNumber, setGiftVoucherNumber] = useState('');
  const [captchaText, setCaptchaText] = useState('');
  const [captchaValue, setCaptchaValue] = useState('');
  const [cardBalance, setCardBalance] = useState('');
  const [balanceToUse, setBalanceToUse] = useState('');
  const [captchaVerified, setCaptchaVerified] = useState(false);
  const [captchaMessage, setCaptchaMessage] = useState('');
  const captchaTimeoutRef = useRef(null);
  const [voucherLoading, setVoucherLoading] = useState(false);
  const [appliedVoucher, setAppliedVoucher] = useState(null);
  const [voucherError, setVoucherError] = useState('');
  const [giftCardApplied, setGiftCardApplied] = useState(false); // Track if gift card is applied but not redeemed
  const [giftCardDetails, setGiftCardDetails] = useState(null); // Store fetched gift card details
  const [giftCardDetailsLoading, setGiftCardDetailsLoading] = useState(false); // Loading state for auto-fetch
  const [appliedGiftItems, setAppliedGiftItems] = useState([]);
  const [appliedGiftItemsLoading, setAppliedGiftItemsLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [redeemedGiftItems, setRedeemedGiftItems] = useState([]);
  const [redeemAmountError, setRedeemAmountError] = useState('');
  const [isRedeemAmountValid, setIsRedeemAmountValid] = useState(false);

  const { addProductsToWishlist } = useProductWishList(props);
  const [redeemedAmountSum, setRedeemedAmountSum] = useState(0);
  const {
    cart,
    setCanCheckout,
    lineItems,
    cartSubTotalAmount,
    cartTotalPayableWithCurrency,
    attribute
  } = useCart(props);

  const [lineItemsWithSelected, setLineItemsWithSelected] = useState([]);

  const customAttributesAction = () => {
    const userData = getUser();
    const isEmployee = userData?.tags?.includes('jockey-employee');
    const defaultAttributes = props?.attributes?.blockItem?.attributes;

    let hasEmployeeAttribute = false;

    try {
      if (defaultAttributes && typeof defaultAttributes === 'object') {
        hasEmployeeAttribute = 'employee' in defaultAttributes;
      }
    } catch (error) {
      console.warn('Error checking employee attribute:', error);
      hasEmployeeAttribute = false;
    }

    if (isEmployee) {
      if (!hasEmployeeAttribute) {
        props?.onAction?.({
          action: 'UPDATE_CART_CUSTOM_ATTRIBUTES',
          params: {
            cartCustomAttributes: [
              { key: 'employee', value: 'true' },
            ],
          },
        });
      }
    } else {
      props?.onAction?.({
        action: 'UPDATE_CART_CUSTOM_ATTRIBUTES',
        params: {
          cartCustomAttributes: [],
        },
      });
    }
  };

  const styles = getLocalStyles();
  useEffect(() => {
    getRecommendation();
    const updatedLineItems = lineItems?.map((item) => ({
      ...item,
      isSelected: false, // or set a condition, e.g., item.field === selectedValue
    }));

    setLineItemsWithSelected(updatedLineItems);

    // Generate initial captcha when component mounts
    generateCaptcha();
  }, [lineItems]);
  useEffect(()=>{
    console.log("callingRedeem",isRedeemAmountValid)
    fetchRedeemedGiftItems()
  },[isRedeemAmountValid])

  useEffect(() => {
    fetchRedeemedGiftItems();
    fetchRedeemedAmountSum();
  }, []);
  // Function to generate random captcha text
  const generateCaptcha = () => {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
    let captcha = '';
    for (let i = 0; i < 6; i++) {
      captcha += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setCaptchaText(captcha);
    setCaptchaValue(''); // Clear previous input
    setCaptchaVerified(false); // Reset verification status
    setCaptchaMessage(''); // Clear message

    // Clear any existing timeout
    if (captchaTimeoutRef.current) {
      clearTimeout(captchaTimeoutRef.current);
      captchaTimeoutRef.current = null;
    }
  };

  // open gc/gv modal
const openGiftCardModal = async() => {
  {
    // Clear form fields and state
    setActiveTab(appliedVoucher ? 'Gift Voucher' : 'Gift Card');
    setGiftCardNumber('');
    setGiftCardPin('');
    setGiftVoucherNumber('');
    setBalanceToUse('');
    setVoucherError('');
    setGiftCardDetails(null);
    generateCaptcha();
    setAppliedGiftItemsLoading(true);
    try {
      const userData = getUser();
      const mobileNumber = userData?.phone || '';
      const cartValue = cart?.totalPrice?.amount || '0';
      const response = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/authDataList', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/" },
        body: JSON.stringify({
          mobilenumber: mobileNumber,
          cartvalue: cartValue
        }),
      });
      // console.log("gcr", response)
      const contentType = response.headers.get('content-type');
      let data = [];
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      }
      console.log("gcr", data)
      setAppliedGiftItems(Array.isArray(data) ? data : []);
      console.log("gcd", appliedGiftItems)
      if (Array.isArray(data) && data.length > 0) {
        setAppliedVoucher(data[0]); // or handle multiple if your modal supports it
        setShowAppliedVoucherModal(true);
        setShowGiftModal(false);
      } else {
        setShowGiftModal(true);
      }
    } catch (error) {
      setAppliedGiftItems([]);
      setShowGiftModal(true); // fallback to input form
    } finally {
      setAppliedGiftItemsLoading(false);
    }
  }
}
  // Clear timeout when component unmounts
  useEffect(() => {
    return () => {
      if (captchaTimeoutRef.current) {
        clearTimeout(captchaTimeoutRef.current);
      }
    };
  }, []);

  // Auto-fetch gift card details when captcha is verified and both fields are filled
  useEffect(() => {
    if (activeTab === 'Gift Card' && giftCardNumber && giftCardPin && captchaVerified) {
      const timeoutId = setTimeout(() => {
        autoFetchGiftCardDetails(giftCardNumber, giftCardPin);
      }, 500); // Wait 500ms after captcha verification

      return () => clearTimeout(timeoutId);
    } else {
      setGiftCardDetails(null);
    }
  }, [giftCardNumber, giftCardPin, activeTab, captchaVerified]);

  // Function to verify captcha when full length is entered
  const verifyCaptcha = (text) => {
    // Clear any existing timeout
    if (captchaTimeoutRef.current) {
      clearTimeout(captchaTimeoutRef.current);
    }

    // Only verify when captcha is complete (same length as generated captcha)
    if (text.length === captchaText.length) {
      if (text === captchaText) {
        setCaptchaVerified(true);
        setCaptchaMessage('Captcha verified');
      } else {
        setCaptchaVerified(false);
        setCaptchaMessage('Invalid captcha');
      }
    } else if (text.length === 0) {
      // Clear message when input is empty
      setCaptchaMessage('');
      setCaptchaVerified(false);
    } else {
      // Don't show any message while user is still typing
      setCaptchaMessage('');
      setCaptchaVerified(false);
    }
  };

  // We already have checkout from useAppStorage at the top of the component
  const resetFields = () => {
    setGiftCardNumber('');
    setGiftCardPin('');
    setGiftVoucherNumber('');
    setBalanceToUse('');
    setVoucherError('');
    setGiftCardDetails(null);
    setCaptchaValue('');
    setCaptchaVerified(false);
    setCaptchaMessage('');
  };
  
  // Function to check voucher details
  const checkVoucherDetails = async (voucherNumber) => {
    if (!voucherNumber || !captchaVerified) return;

    setVoucherLoading(true);
    setVoucherError('');

    try {
      const cartToken = (checkout?.id?.split('/').pop() || '').split('?')[0];
      const cartValue = cart?.totalPrice?.amount || '0';
      const userData = getUser();
      const customerId = userData?.id || '';
      const mobileNumber = userData?.phone || '';

      const response = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/voucherDetails', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/" },
        body: JSON.stringify({
          cartToken,
          cartvalue: cartValue,
          customerid: getIdFromGID(customerId), // Extract just the ID from GID to match web
          mobilenumber: mobileNumber,
          type: 'voucher',
          voucher: voucherNumber
        }),
      });
      // Check if response is valid JSON before parsing
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        // Not a JSON response
        const text = await response.text();
        console.error('Non-JSON response received:', text.substring(0, 100)); // Log first 100 chars
        throw new Error('Server returned an invalid response format');
      }

      const data = await response.json();
      if (data.Code === "00") {
        setAppliedVoucher({
          type: 'voucher',
          number: data.number,
          amount: data.redeempoints || data.amount || 0,
          applied: false,
          redeemed: false,
          voucherData: data
        });
        setShowGiftModal(false);
        // Fetch latest applied items and open listing modal
        await fetchAppliedGiftItems();
        setShowAppliedVoucherModal(true);
        // Optionally, call getAuthDataList in the background to sync
        // getAuthDataList(mobileNumber, cartValue, data);
      } else {
        setVoucherError(data.error || 'Invalid voucher');
      }
    } catch (error) {
      console.error('Error checking voucher:', error);
      setVoucherError('Failed to validate voucher. Please try again later.');
    } finally {
      setVoucherLoading(false);
    }
  };

  // Function to get auth data list
  const getAuthDataList = async (mobileNumber, cartValue, voucherData) => {
    try {
      const response = await fetch('https://www.jockeyuat.online/apps/jr/authDataList', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/" },
        body: JSON.stringify({
          cartvalue: cartValue,
          mobilenumber: mobileNumber
        }),
      });

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const text = await response.text();
        console.error('Non-JSON response received from authDataList:', text.substring(0, 100));
        throw new Error('Server returned an invalid response format');
      }

      const data = await response.json();
      console.log('authDataList response:', data, 'voucherData.number:', voucherData.number);

      // Check if the voucher is in the auth data list
      const matchingVoucher = data.find(item =>
        String(item.number).trim() === String(voucherData.number).trim()
      );

      if (matchingVoucher) {
        // For vouchers, first step is just to show in applied list
        // The actual redeem will happen when user clicks "APPLY" in the applied modal
        setAppliedVoucher({
          type: 'voucher',
          number: matchingVoucher.number,
          amount: matchingVoucher.amount,
          applied: false, // Not yet applied/redeemed, just validated
          redeemed: false,
          voucherData: voucherData // Store voucher data for later redeem step
        });

        // Close the gift modal and show the applied voucher modal
        setShowGiftModal(false);
        setShowAppliedVoucherModal(true);
      } else {
        setVoucherError('Voucher not authorized');
      }
    } catch (error) {
      console.error('Error getting auth data list:', error);
      setVoucherError('Failed to authorize voucher. Please try again later.');
    }
  };

  // Function to automatically fetch gift card details when captcha is verified
  const autoFetchGiftCardDetails = async (giftCardNumber, pin) => {
    if (!giftCardNumber || !pin) {
      setGiftCardDetails(null);
      return;
    }

    setGiftCardDetailsLoading(true);
    setVoucherError('');

    try {
      const cartToken = (checkout?.id?.split('/').pop() || '').split('?')[0];
      const cartValue = cart?.totalPrice?.amount || '0';
      const userData = getUser();
      const customerId = userData?.id || '';
      const mobileNumber = userData?.phone || '';

      const payload = {
        type: 'giftcard',
        voucher: giftCardNumber,
        pin: pin,
        customerid: getIdFromGID(customerId), // Extract just the ID from GID to match web
        mobilenumber: mobileNumber,
        cartvalue: cartValue,
        cartToken: cartToken
      };

      // Console log the payload as requested
      console.log('Auto-fetch Gift Card Payload:', payload);

      const response = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/voucherDetails', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/" },
        body: JSON.stringify(payload),
      });

      // Check if response is valid JSON before parsing
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        // Not a JSON response
        const text = await response.text();
        console.error('Non-JSON response received:', text.substring(0, 100)); // Log first 100 chars
        throw new Error('Server returned an invalid response format');
      }

      const data = await response.json();
      console.log("Auto-fetch giftCard response:", data);

      if (data.Code === "00") {
        // Gift card is valid, store the details
        setGiftCardDetails({
          number: data.number,
          totalavailablepoints: data.totalavailablepoints,
          expirytime: data.expirytime,
          provider: data.provider,
          pin: pin // Store the PIN from the input, not from response
        });

        // Auto-fill amount for Razorpay gift cards and disable input
        if (data.provider && data.provider.toLowerCase() === 'razorpay') {
          setBalanceToUse(data.redeempoints.toString());
        }

        setVoucherError('');
      } else {
        setGiftCardDetails(null);
        setVoucherError(data.error || 'Invalid gift card details');
      }
    } catch (error) {
      console.error('Error auto-fetching gift card details:', error);
      setGiftCardDetails(null);
      setVoucherError('Failed to fetch gift card details');
    } finally {
      setGiftCardDetailsLoading(false);
    }
  };

  // Function to check gift card details
  const checkGiftCardDetails = async (giftCardNumber, pin) => {
    if (!giftCardNumber || !pin || !captchaVerified) return;

    setVoucherLoading(true);
    setVoucherError('');

    try {
      const cartToken = (checkout?.id?.split('/').pop() || '').split('?')[0];
      const cartValue = cart?.totalPrice?.amount || '0';
      const userData = getUser();
      const customerId = userData?.id || '';
      const mobileNumber = userData?.phone || '';

      const payload = {
        type: 'giftcard',
        voucher: giftCardNumber,
        pin: pin,
        customerid: getIdFromGID(customerId), // Extract just the ID from GID to match web
        mobilenumber: mobileNumber,
        cartvalue: cartValue,
        cartToken: cartToken
      };

      // Console log the payload as requested
      console.log('Gift Card Payload:', payload);

      const response = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/voucherDetails', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/" },
        body: JSON.stringify(payload),
      });

      // Check if response is valid JSON before parsing
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        // Not a JSON response
        const text = await response.text();
        console.error('Non-JSON response received:', text.substring(0, 100)); // Log first 100 chars
        throw new Error('Server returned an invalid response format');
      }

      const data = await response.json();
console.log("giftCard", data)
      if (data.Code === "00") {
        // Gift card is valid, store the details for applying
        setGiftCardDetails({
          number: data.number,
          totalavailablepoints: data.totalavailablepoints,
          expirytime: data.expirytime,
          provider: data.provider,
          pin: pin // Store the PIN from the input
        });

        // Auto-fill amount for Razorpay gift cards and disable input
        if (data.provider && data.provider.toLowerCase() === 'razorpay') {
          setBalanceToUse(data.totalavailablepoints.toString());
        }

        setVoucherError('');
      } else {
        setVoucherError(data.error || 'Invalid gift card');
      }
    } catch (error) {
      console.error('Error checking gift card:', error);
      setVoucherError('Failed to validate gift card. Please try again later.');
    } finally {
      setVoucherLoading(false);
    }
  };

  // Function to apply gift card with user-entered amount
  const applyGiftCardWithAmount = async () => {
    if (!giftCardDetails || !balanceToUse) {
      setVoucherError('Please enter the amount to use');
      return;
    }

    const appliedAmount = parseFloat(balanceToUse);
    if (isNaN(appliedAmount) || appliedAmount <= 0) {
      setVoucherError('Please enter a valid amount');
      return;
    }

    // New validation: amount should not exceed cart value
    const cartValueNum = parseFloat(checkout?.totalPrice?.amount || '0');
    if (appliedAmount > cartValueNum) {
      setVoucherError('Amount exceeds the cart value');
      return;
    }

    if (appliedAmount > giftCardDetails.totalavailablepoints || appliedAmount > giftCardDetails.redeempoints) {
      setVoucherError(`Amount cannot exceed available balance of ₹${giftCardDetails.totalavailablepoints}`);
      return;
    }

    setVoucherLoading(true);
    setVoucherError('');

    try {
      // Use the existing gift card details (no extra voucherDetails call)
      const cartToken = ((checkout?.id?.split('/').pop() || '').split('?')[0]).substring(0, 15);
      const cartValue = Math.floor(parseFloat(cart?.totalPrice?.amount || '0')).toString();
      const userData = getUser();
      const mobileNumber = userData?.phone || '';

      // Apply directly with existing data (match web flow exactly)
      const applyPayload = {
        Code: "00",
        type: "giftcard",
        provider: giftCardDetails.provider || "qwikcilver",
        number: giftCardDetails.number,
        pin: giftCardDetails.pin,
        redeempoints: "",
        mobilenumber: mobileNumber,
        expirytime: giftCardDetails.expirytime,
        totalavailablepoints: giftCardDetails.totalavailablepoints,
        appliedamount: appliedAmount,
        cartvalue: cartValue,
        cartToken: cartToken
      };

      // Console log the apply payload
      console.log('Apply Gift Card Payload:', applyPayload);

      const response = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/applyGiftCard', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/" },
        body: JSON.stringify(applyPayload),
      });
console.log("responseApp", response)
      // Check if response is valid JSON before parsing
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const text = await response.text();
        console.error('Non-JSON response received from applyGiftCard:', text.substring(0, 100));
        throw new Error('Server returned an invalid response format');
      }

      const data = await response.json();
      console.log('ApplyGiftCard', data);

      if (data.Code === "00") {
        // Gift card applied successfully
        console.log('Gift card applied successfully, waiting before fetching applied items');

        // Add a small delay to allow the apply operation to be processed
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second

        // Close the gift modal first
        setShowGiftModal(false);

        // Clear form fields
        setGiftCardNumber('');
        setGiftCardPin('');
        setBalanceToUse('');
        setGiftCardDetails(null);
        setCaptchaValue('');
        setCaptchaVerified(false);
        setCaptchaMessage('');
        generateCaptcha();

        // Fetch latest applied items and open listing modal
        console.log('Now fetching applied items');
        await fetchAppliedGiftItems();
        setShowAppliedVoucherModal(true);
      } else {
        setVoucherError(data.error || 'Failed to apply gift card');
      }
    } catch (error) {
      console.error('Error applying gift card:', error);
      setVoucherError('Failed to apply gift card. Please try again later.');
    } finally {
      setVoucherLoading(false);
    }
  };



  const updateWishlist = (lineItem) => {
    addProductsToWishlist({ productIds: [lineItem?.variant?.product?.id] });
    if (!customerId) return;
    const productId = getIdFromGID(lineItem?.variant?.product?.id);
    const variantId = getIdFromGID(lineItem?.variant?.id);
    updateWebWishlist(customerId, productId, variantId);
  };

  const updateAllWishlistItems = () => {
    const selectedIds = lineItemsWithSelected
      ?.filter((item) => item?.isSelected)
      .map((item) => item?.node?.variant?.product?.id);

    addProductsToWishlist({ productIds: selectedIds });

    if (!customerId) return;
    for (let index = 0; index < lineItemsWithSelected.length; index++) {
      const lineItem = lineItemsWithSelected[index]?.node;
      const productId = getIdFromGID(lineItem?.variant?.product?.id);
      const variantId = getIdFromGID(lineItem?.variant?.id);
      updateWebWishlist(customerId, productId, variantId);
    }
  };

  const openProductByHandle = (handle) => {
    handleAction({
      action: 'OPEN_PRODUCT',
      params: {
        productHandle: handle,
      },
    });
  };

  // Removed unused onLineItemUpdate function

  const onLineItemReplace = (itemsToAdd, itemToDelete) => {
    // return;
    const foundItem = lineItemsWithSelected.find(
      (item) => item?.node?.variant?.id === itemsToAdd?.[0]?.variantId,
    );
    // console.log("onLineItemReplace", foundItem);

    if (foundItem) {
      let lineItems = {
        id: foundItem?.node?.id,
        variantId: foundItem?.node?.variant?.id,
        quantity: foundItem?.node?.quantity + itemsToAdd?.[0]?.quantity,
        customAttributes: foundItem?.node?.customAttributes,
      };

      manageCart({
        lineItemsToUpdate: [lineItems],
        lineItemsToRemove: itemToDelete,
        updateCartPageStateRequired: true,
      });
      return;
    }

    manageCart({
      lineItemsToAdd: itemsToAdd,
      lineItemsToRemove: itemToDelete,
      // showMessage: true,
      updateCartPageStateRequired: true,
    });
  };

  const onLineItemDelete = (itemsToDelete) => {
    // console.log("itemToUpdate", itemsToDelete);
    // return;

    manageCart({
      lineItemsToRemove: itemsToDelete,
      //   showMessage: true,
      updateCartPageStateRequired: true,
    });
  };

  const getRecommendation = async () => {
    try {
      const [response] = await runDataSource(
        {
          dataSource,
        },
        {
          methodName: 'gqlQuery',
          params: {
            query: `
        {
 productRecommendations(productId:"${lineItems?.[0]?.node?.variant?.product?.id}") {
   images(first: 1) {
      edges {
        node {
          url
        }
      }
    }
    id
    totalInventory
    handle
    title
    tags
    metafields(identifiers: [{ key: "style_number", namespace: "custom" }, { key: "colour_family", namespace: "custom" }]) {
          value
        }
          style_number: metafield(key: "style_number", namespace: "custom") {
             value
           }
             uom_quantity: metafield(key: "uom_quantity", namespace: "custom") {
             value
           }
          color_variant: metafield(key: "color_variant", namespace: "custom") {
          value
      }
        metafield(key: "color_variant", namespace: "custom") {
          value
          reference {
            ... on Product {
              images(first: 7) {
                nodes {
                  url
                  altText
                }
              }
              handle
              totalInventory
              title
              tags
              options(first: 10) {
                values
                name
              }
            }
          }
        }
    priceRange {
      maxVariantPrice {
        amount
        currencyCode
      }
      minVariantPrice {
        amount
        currencyCode
      }
    }
    compareAtPriceRange {
      maxVariantPrice {
        amount
        currencyCode
      }
      minVariantPrice {
        amount
        currencyCode
      }
    }
       variants(first: 20) {
      edges {
        node {
          id
          sku
          title
          quantityAvailable
          availableForSale
          selectedOptions {
            name
            value
          }
        }
      }
    }
      options {
      values
      name
    }
  }
}

        `,
          },
        },
      );

      let data = response?.data?.data?.productRecommendations;
      setRecommendation(data);
    } catch (error) {
      console.log('getRecommendation error', error);
    }
  };

  const showModel = async (nodes) => {
    if (nodes) {
      setSelectedProductData(nodes);
      setShowModal(true);
    }
  };

  const isAllSelected = lineItemsWithSelected?.every((item) => item.isSelected);

  const selectedCount = lineItemsWithSelected?.filter(
    (item) => item.isSelected === true,
  ).length;


  const renderHeader = () => {
    return (
      <View style={[{ paddingTop: insets.top }]}>
        <View
          style={{
            height: widthPixel(58),
            backgroundColor: 'white',
            width: '100%',
            paddingVertical: widthPixel(14),
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: widthPixel(20),
            shadowRadius: widthPixel(2),
            shadowColor: '#221f2026',
            shadowOpacity: 0.1, // Adjust shadow visibility
            shadowOffset: { width: 0, height: widthPixel(2) },
            borderColor: 'grey',
            // marginBottom: widthPixel(10),
          }}>
          <View style={{ flexDirection: 'row', gap: widthPixel(10), alignItems: 'center', flex: 1, }}>
            <Ripple onPress={() => navigation.goBack()}>
              <Svg
                xmlns="http://www.w3.org/2000/svg"
                width="26"
                height="26"
                viewBox="0 0 26 26"
                fill="none">
                <G id="Arrow / Arrow_Left_MD">
                  <Path
                    id="Vector"
                    d="M20.5832 13L5.4165 13M5.4165 13L11.9165 19.5M5.4165 13L11.9165 6.5"
                    stroke="#221F20"
                    strokeWidth="2.3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </G>
              </Svg>
            </Ripple>

            <View style={{ flex: 1, }}>
              <Text
                style={styles.itemInCartText}
                numberOfLines={1}>

                Items in my bag

              </Text>

            </View>
          </View>

          <ToolbarIcon
            attributes={{
              routeName: "WishList",
              icon: WishlistIcon,
              itemCount: products?.length,
            }}
            onPress={() => {
              props?.onAction({
                action: 'OPEN_INAPP_PAGE',
                pageId: 'WishList',
              })

            }} />

        </View>
      </View>
    );
  };

  // Fetch applied GC/GV when modal opens
  const fetchAppliedGiftItems = async () => {
    setAppliedGiftItemsLoading(true);
    try {
      const userData = getUser();
      const mobileNumber = userData?.phone || '';
      const cartValue = cart?.totalPrice?.amount || '0';
      const response = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/authDataList', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/" },
        body: JSON.stringify({
          mobilenumber: mobileNumber,
          cartvalue: cartValue
        }),
      });
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        setAppliedGiftItems([]);
        return;
      }
      const data = await response.json();
      console.log("data", data)
      setAppliedGiftItems(Array.isArray(data) ? data : []);
      // setShowAppliedVoucherModal(true);
    } catch (error) {
      setAppliedGiftItems([]);
    } finally {
      setAppliedGiftItemsLoading(false);
    }
  };
  const fetchRedeemedAmountSum = async () => {
    try {
      const userData = getUser();
      const mobileNumber = userData?.phone || '';
      const encodedMobile = Buffer.from(mobileNumber).toString('base64');
      const response = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/redeemAmountSum', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/" },
        body: JSON.stringify({ mobilenumber: encodedMobile }),
      });
      const data = await response.json();
      console.log("--redeemSum", data)

      const cartValue = parseFloat(cart?.totalPrice?.amount || '0');
      const totalRedeemAmount = Number(data.TotalRedeemAmount) || 0;
      console.log("--totalRedeemAmount", totalRedeemAmount,cartValue)
      setRedeemedAmountSum(totalRedeemAmount);

      if (data.Code === "00") {
        if (totalRedeemAmount <= cartValue) {
          console.log("trureRE",totalRedeemAmount <= cartValue )
          setIsRedeemAmountValid(true);
          // await fetchRedeemedGiftItems()
        } else {
          setIsRedeemAmountValid(false);
          setRedeemAmountError('Total redeemed amount exceeds cart value. Please remove some gift cards or vouchers.');
        }
      }
    } catch (e) {
      console.error("Error in fetchRedeemedAmountSum:", e);
      setRedeemedAmountSum(0);
      setIsRedeemAmountValid(true);
    }
  };

  const updateCartValueAll =(codes)=>{
    console.log("--callinAll", codes)
    props?.onAction?.({
      action: 'CHECKOUT_APPLY_GIFT_CARD',
      params: { giftCardCodes: codes }
    });
  }
  const updateCartValueOne = (validGCCodes) => {
    try {
      console.log("--callingup", validGCCodes[0].gccode);
      props?.onAction?.({
        action: 'CHECKOUT_APPLY_GIFT_CARD',
        params: {
          giftCardCode: validGCCodes[0].gccode,
          updateCartPageStateRequired: true
        }
      });
    } catch (error) {
      console.error("Error while applying gift card:", error);
    }
  };
  
  const fetchRedeemedGiftItems = async () => {
    console.log("--redeemed gift items")
    try {
      const userData = getUser();
      const mobileNumber = userData?.phone || '';
      const encodedMobile = Buffer.from(mobileNumber).toString('base64');
      console.log("mobile", encodedMobile)
      const response = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/redeemDataList', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/", },
        body: JSON.stringify({ mobilenumber: encodedMobile }),
      });
      const data = await response.json();
      console.log("redeemed gift items data", data)
      setRedeemedGiftItems(Array.isArray(data) ? data : []);
      
      // Wait for checkout ID to be available
      if (!checkout?.id) {
        console.log("Waiting for checkout ID...");
        return;
      }
      
      // Extract cart token
      const cartToken = (checkout?.id?.split('/').pop() || '').split('?')[0];
      console.log("Cart Token:", cartToken);
      
      // Process gift card data for cart attributes
      let providers = '';
      let gcNumbers = '';
      let gcAmounts = '';
      let shopify_gccodes = '';
      
      if (Array.isArray(data) && data.length > 0) {
        providers = data.map(card => card.provider || '').join(',');
        gcNumbers = data.map(card => card.number || '').join(',');
        gcAmounts = data.map(card => card.amount || '').join(',');
        shopify_gccodes = data.filter(card => card.gccode).map(card => card.gccode || '').join(',');
      }

      // Prepare cart attributes
      const cartAttributes = [
        { key: "cart_token", value: cartToken || "" },
        { key: "giftcard", value: Array.isArray(data) ? JSON.stringify(data) : "" },
        { key: "provider", value: providers || "" },
        { key: "gc_gv_number", value: gcNumbers || "" },
        { key: "gc_gv_amount", value: gcAmounts || "" },
        { key: "shopify_gc", value: shopify_gccodes || "" },
      ];

      // console.log("Updating Cart Attributes:", JSON.stringify(cartAttributes, null, 2));

      // Update cart attributes
      if (props?.onAction) {
        props.onAction({
          action: 'UPDATE_CART_CUSTOM_ATTRIBUTES',
          params: {
            cartCustomAttributes: cartAttributes,
          },
        });

        // Log the cart attributes after a delay to verify the update
        setTimeout(() => {
          console.log("Current Cart Attributes cart:", cart?.customAttributes);
        }, 1000);
      } else {
        console.error("onAction prop is not available");
      }

      const validGCCodes = data.filter(item => item.gccode);
      console.log("isRedeemAmountValid", isRedeemAmountValid)
      // Only apply gift cards if isRedeemAmountValid is true
      if (isRedeemAmountValid) {
        if (validGCCodes.length > 1) {
          const codes = validGCCodes.map(item => item.gccode);
          console.log("Calling Appmaker action for multiple codes", codes);
          updateCartValueAll(codes)
        } else if (validGCCodes.length === 1) {
          console.log("Calling Appmaker action for single code", validGCCodes[0].gccode);
          updateCartValueOne(validGCCodes)
        }
      } else {
        console.log('Total redeem amount exceeds cart value, not applying gift cards.');
      }
    } catch (e) {
      // console.error("Error in fetchRedeemedGiftItems:", e);
      setRedeemedGiftItems([]);
    }
  };

  const refundCartValueAll =(codes)=>{
    console.log("--callinAll", codes)
    props?.onAction?.({
      action: 'CHECKOUT_APPLY_GIFT_CARD',
      params: { giftCardCodes: codes }
    });
  }
  const refundCartValueOne =(gcnumber)=>{
    console.log("--callingRefund", gcnumber)
    props?.onAction?.({
      action: 'CART_REMOVE_GIFT_CARD',
      params: {
        giftCardCode: "648439726382"
      }
    });
  }

 

  return (
    <>
      {renderHeader()}

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      // stickyHeaderIndices={[0]}
      >
        {/* <View style={[styles.headerContainer]}>
          <Text style={styles.itemInCartText}>Items in my bag</Text>
        </View> */}

        <View style={styles.mainContainer}>
          <View style={styles.multiSelectContainer}>
            <View style={styles.rowContainer}>
              {isAllSelected ? (
                <AppTouchable
                  style={{ padding: 5 }}
                  onPress={() => {
                    const updatedLineItems = lineItems.map((item) => ({
                      ...item,
                      isSelected: false, // or set a condition, e.g., item.field === selectedValue
                    }));

                    setLineItemsWithSelected(updatedLineItems);
                  }}>
                  <Svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 18 18"
                    fill="none">
                    <Circle cx="9" cy="9" r="9" fill="#221F20" />
                    <Path
                      d="M12.557 6.75781L7.9524 11.2311L5.85938 9.1978"
                      fill="#221F20"
                    />
                    <Path
                      d="M12.557 6.75781L7.9524 11.2311L5.85938 9.1978"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </Svg>

                  {/* <Svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <Circle cx="9" cy="9" r="9" fill={"rgb(34, 31, 32)"} />
                                        <Path d="M12.557 6.75781L7.9524 11.2311L5.85938 9.1978" fill="#CFCFCF" />
                                        <Path
                                            d="M12.557 6.75781L7.9524 11.2311L5.85938 9.1978"
                                            stroke="white"
                                            strokeWidth="2"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                        />
                                    </Svg> */}
                </AppTouchable>
              ) : selectedCount > 0 ? (
                <Svg
                  class="icon-select-active"
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 18 18"
                  fill="none">
                  <Circle cx="9" cy="9" r="9" fill="#221F20"></Circle>
                  <Path
                    d="M12.0005 9.00049L6.00049 9.04248"
                    stroke="white"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"></Path>
                </Svg>
              ) : (
                <AppTouchable
                  style={{ padding: 5 }}
                  onPress={() => {
                    const updatedLineItems = lineItems.map((item) => ({
                      ...item,
                      isSelected: true, // or set a condition, e.g., item.field === selectedValue
                    }));

                    setLineItemsWithSelected(updatedLineItems);
                  }}>
                  <Svg
                    width="18"
                    height="18"
                    viewBox="0 0 18 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <Circle cx="9" cy="9" r="9" fill={'#CFCFCF'} />
                    <Path
                      d="M12.557 6.75781L7.9524 11.2311L5.85938 9.1978"
                      fill="#CFCFCF"
                    />
                    <Path
                      d="M12.557 6.75781L7.9524 11.2311L5.85938 9.1978"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </Svg>
                </AppTouchable>
              )}

              <Text
                style={[
                  styles.selectedText,
                  {
                    color: selectedCount > 0 ? '#000' : 'grey',
                    fontFamily:
                      selectedCount > 0 ? 'Jost-Medium' : 'Jost-Regular',
                  },
                ]}>
                {selectedCount > 0
                  ? 'Items selected - ' +
                  selectedCount +
                  '/' +
                  lineItemsWithSelected.length
                  : 'Items added - ' + lineItemsWithSelected?.length}
              </Text>
            </View>

            <View style={styles.rowEndContainer}>
              <Ripple
                style={{ padding: 5 }}
                onPress={() => {
                  setShowConfirmation(true);
                }}
                disabled={selectedCount === 0}>
                {selectedCount > 0 ? (
                  <Svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none">
                    <Path
                      d="M3.11719 5.41211H4.64648H16.8809"
                      stroke="#221F20"
                      strokeWidth="1.10109"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <Path
                      d="M15.3535 5.41211V16.1172C15.3535 16.5228 15.1924 16.9118 14.9056 17.1986C14.6188 17.4854 14.2298 17.6465 13.8242 17.6465H6.17773C5.77214 17.6465 5.38316 17.4854 5.09636 17.1986C4.80956 16.9118 4.64844 16.5228 4.64844 16.1172V5.41211M6.94238 5.41211V3.88281C6.94238 3.47722 7.1035 3.08824 7.3903 2.80144C7.6771 2.51464 8.06608 2.35352 8.47168 2.35352H11.5303C11.9359 2.35352 12.3248 2.51464 12.6116 2.80144C12.8984 3.08824 13.0596 3.47722 13.0596 3.88281V5.41211"
                      stroke="#221F20"
                      strokeWidth="1.10109"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <Path
                      d="M8.4707 9.23438V13.8223"
                      stroke="#221F20"
                      strokeWidth="1.10109"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <Path
                      d="M11.5293 9.23438V13.8223"
                      stroke="#221F20"
                      strokeWidth="1.10109"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </Svg>
                ) : (
                  <Svg
                    class="icon-delete"
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none">
                    <Path
                      d="M3.11816 5.41211H4.64746H16.8818"
                      stroke="#787777"
                      stroke-width="1.10109"
                      stroke-linecap="round"
                      stroke-linejoin="round"></Path>
                    <Path
                      d="M15.3525 5.41211V16.1172C15.3525 16.5228 15.1914 16.9118 14.9046 17.1986C14.6178 17.4854 14.2288 17.6465 13.8232 17.6465H6.17676C5.77116 17.6465 5.38218 17.4854 5.09538 17.1986C4.80858 16.9118 4.64746 16.5228 4.64746 16.1172V5.41211M6.94141 5.41211V3.88281C6.94141 3.47722 7.10253 3.08824 7.38933 2.80144C7.67613 2.51464 8.06511 2.35352 8.4707 2.35352H11.5293C11.9349 2.35352 12.3239 2.51464 12.6107 2.80144C12.8975 3.08824 13.0586 3.47722 13.0586 3.88281V5.41211"
                      stroke="#787777"
                      stroke-width="1.10109"
                      stroke-linecap="round"
                      stroke-linejoin="round"></Path>
                    <Path
                      d="M8.4707 9.23438V13.8223"
                      stroke="#787777"
                      stroke-width="1.10109"
                      stroke-linecap="round"
                      stroke-linejoin="round"></Path>
                    <Path
                      d="M11.5293 9.23438V13.8223"
                      stroke="#787777"
                      stroke-width="1.10109"
                      stroke-linecap="round"
                      stroke-linejoin="round"></Path>
                  </Svg>
                )}
              </Ripple>

              <View style={styles.separator} />

              <Ripple
                style={{ padding: 5 }}
                onPress={() => {
                  setShowWishlistConfirmation(true);
                }}
                disabled={selectedCount === 0}>
                {selectedCount > 0 ? (
                  <Svg
                    class="icon-moveToWishlist-active"
                    onclick="wishlistCartItems_Modal();wishlistBarClose();"
                    xmlns="http://www.w3.org/2000/svg"
                    width="21"
                    height="20"
                    viewBox="0 0 21 20"
                    fill="none">
                    <Path
                      d="M12.668 7.48531C11.1124 3.66149 5.66797 4.06876 5.66797 8.95605C5.66797 13.8433 12.668 17.916 12.668 17.916C12.668 17.916 19.668 13.8433 19.668 8.95605C19.668 4.06876 14.2235 3.66149 12.668 7.48531Z"
                      stroke="#2F2C2D"
                      stroke-width="1.2"
                      stroke-linecap="round"
                      stroke-linejoin="round"></Path>
                    <Path
                      d="M0.488281 9.69647H7.86726L7.7511 9H9.41777L14.8408 12.2191L9.41777 13.645L5.8003 13.9595L1.20144 14.7456L0.488281 9.69647Z"
                      fill="#FAFAFA"></Path>
                    <Path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M11.7484 9.14314C11.5567 8.94437 11.2401 8.93864 11.0414 9.13034C10.8426 9.32204 10.8369 9.63857 11.0286 9.83733L12.3403 11.1974L1.49558 11.1974C1.49535 11.1974 1.49511 11.1974 1.49488 11.1974H1.39258C1.11644 11.1974 0.892578 11.4212 0.892578 11.6974C0.892578 11.9735 1.11644 12.1974 1.39258 12.1974L12.31 12.1974L10.9386 13.5687C10.7434 13.764 10.7434 14.0806 10.9386 14.2758C11.1339 14.4711 11.4505 14.4711 11.6457 14.2758L13.8681 12.0535C13.888 12.0338 13.9064 12.0125 13.9229 11.9895C13.9294 11.9805 13.9356 11.9713 13.9415 11.9618C13.9894 11.8849 14.0153 11.7968 14.017 11.7072C14.0182 11.6509 14.0098 11.594 13.9914 11.5389C13.9797 11.5039 13.9641 11.4701 13.9447 11.4381C13.9392 11.4291 13.9335 11.4203 13.9274 11.4116C13.9114 11.3885 13.8935 11.3671 13.8741 11.3473L11.7484 9.14314Z"
                      fill="#221F20"></Path>
                  </Svg>
                ) : (
                  <Svg
                    class="icon-moveToWishlist"
                    xmlns="http://www.w3.org/2000/svg"
                    width="21"
                    height="20"
                    viewBox="0 0 21 20"
                    fill="none">
                    <Path
                      d="M12.667 7.48531C11.1114 3.66149 5.66699 4.06876 5.66699 8.95605C5.66699 13.8433 12.667 17.916 12.667 17.916C12.667 17.916 19.667 13.8433 19.667 8.95605C19.667 4.06876 14.2225 3.66149 12.667 7.48531Z"
                      stroke="#787777"
                      stroke-width="1.2"
                      stroke-linecap="round"
                      stroke-linejoin="round"></Path>
                    <Path
                      d="M0.487305 9.69647H7.86629L7.75012 9H9.41679L14.8398 12.2191L9.41679 13.645L5.79933 13.9595L1.20047 14.7456L0.487305 9.69647Z"
                      fill="#FAFAFA"></Path>
                    <Path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M11.7474 9.14314C11.5557 8.94437 11.2392 8.93864 11.0404 9.13034C10.8416 9.32204 10.8359 9.63857 11.0276 9.83733L12.3393 11.1974L1.4946 11.1974C1.49437 11.1974 1.49414 11.1974 1.4939 11.1974H1.3916C1.11546 11.1974 0.891602 11.4212 0.891602 11.6974C0.891602 11.9735 1.11546 12.1974 1.3916 12.1974L12.309 12.1974L10.9376 13.5687C10.7424 13.764 10.7424 14.0806 10.9376 14.2758C11.1329 14.4711 11.4495 14.4711 11.6447 14.2758L13.8671 12.0535C13.8871 12.0338 13.9054 12.0125 13.9219 11.9895C13.9284 11.9805 13.9346 11.9713 13.9405 11.9618C13.9884 11.8849 14.0143 11.7968 14.0161 11.7072C14.0172 11.6509 14.0088 11.594 13.9904 11.5389C13.9787 11.5039 13.9631 11.4701 13.9437 11.4381C13.9383 11.4291 13.9325 11.4203 13.9264 11.4116C13.9104 11.3885 13.8926 11.3671 13.8731 11.3473L11.7474 9.14314Z"
                      fill="#787777"></Path>
                  </Svg>
                )} 
              </Ripple>
            </View>
          </View>

          {lineItemsWithSelected?.map((item) => {
            let lineItem = item.node;
            const isEmployeeDiscount = lineItem?.discountAllocations?.some(
              (allocation) =>
                allocation?.discountApplication?.title === 'Employee Benefits',
            );
            return (
              <CartLineItemCard
                isDiscount={isEmployeeDiscount}
                title={lineItem?.title}
                imageUri={lineItem?.variant?.image?.url}
                variationText={lineItem?.variant?.title}
                quantity={lineItem?.quantity}
                onRemoveItem={onLineItemDelete}
                removeLoading={false}
                openProduct={() => {
                  openProductByHandle(lineItem?.variant?.product?.handle);
                }}
                canRemoveItem={true}
                onWishlistParent={() => {
                  updateWishlist(lineItem);
                }}
                price={lineItem?.variant?.price?.amount}
                currencyCode={lineItem?.variant?.price?.currencyCode}
                compareAtPrice={lineItem?.variant?.compareAtPrice?.amount}
                customAttributes={lineItem?.customAttributes}
                handleValue={lineItem?.variant?.product?.handle}
                // onLineItemUpdate={onLineItemUpdate}
                onLineItemReplace={onLineItemReplace}
                id={lineItem.id}
                isSelected={item?.isSelected}
                onSelect={() => {
                  const updatedLineItems = lineItemsWithSelected.map((item) =>
                    item?.node?.id === lineItem.id
                      ? { ...item, isSelected: !item?.isSelected }
                      : item,
                  );

                  setLineItemsWithSelected(updatedLineItems);
                }}
                userData={data?.customer}
                parentProps={props}
              />
            );
          })}
          <View style={styles.giftCardContainer}>
            <View style={styles.giftCardIconContainer}>
              <GiftCardIcon width={24} height={24} />
            </View>
            <Text style={styles.giftCardText}>Gift Card/ Gift Voucher</Text>
            <TouchableOpacity
              style={styles.addButton}
              onPress={() =>{
                if(isLoggedin){
                  openGiftCardModal()
                }else{
                  handleAction({
                    action: 'OPEN_INAPP_PAGE',
                    pageId: 'LoginOptions',
                    params: { isFromCart: true },
                  });
                }
                }}
            >
              <Text style={styles.addButtonText}>ADD</Text>
            </TouchableOpacity>
          </View>
          {redeemedGiftItems.length > 0 && (
        <View style={{ marginBottom: 16 }}>
          {redeemedGiftItems.map((item, idx) => (
            <View key={item.number + idx} style={styles.appliedVoucherContainer}>
              <View style={styles.voucherInfoContainer}>
                <GiftCardIcon width={24} height={24} />
                <View style={styles.voucherDetailsContainer}>
                  <Text style={styles.voucherTypeText}>
                    {item.provider === 'vouchergram' ? 'Gift Voucher' : 'Gift Card'}
                  </Text>
                  <Text style={styles.voucherNumberText}>{item.number}</Text>
                </View>
              </View>
              <View style={styles.voucherRightContainer}>
                <Text style={styles.voucherAmountText}>₹{item.amount}</Text>
                <TouchableOpacity
                  style={styles.removeVoucherButton}
                  onPress={async () => {
                    try {
                      const refundRes = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/refund', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/" },
                        body: JSON.stringify({ number: item.number }),
                      });
                      const refundData = await refundRes.json();
                      if (refundData.Code === '00') {
                        await fetchRedeemedGiftItems();
                        refundCartValueOne(item.id)
                      }
                    } catch (e) {}
                  }}
                >
                  <Svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <Path d="M15 5L5 15" stroke="#221F20" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <Path d="M5 5L15 15" stroke="#221F20" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </Svg>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>
      )}
          {getVerticalPadding(16)}

          <Text style={styles.youMayLikeText}>You will also like</Text>

          {getVerticalPadding(8)}

          <FlatList
            horizontal
            style={{ marginEnd: -widthPixel(16) }}
            showsHorizontalScrollIndicator={false}
            renderItem={({ item }) => (
              <Card
                props={props}
                item={item}
                openProductByHandle={openProductByHandle}
                showModel={showModel}
              />
            )}
            keyExtractor={(_, index) => index.toString()}
            data={recommendation?.slice(0, 4)}
          />

          {getVerticalPadding(30)}
        </View>

        <CartSummaryTable
          cartSubTotalAmount={cartTotalPayableWithCurrency}
          userData={data?.customer}
          appliedVoucher={appliedVoucher}
        />

        {showConfirmation && (
          <Confirmations
            onClose={() => setShowConfirmation(false)}
            onDelete={() => {
              // const selectedIds = lineItemsWithSelected
              //     ?.filter(item => item?.isSelected)
              //     .map(item => item?.node?.id);
              // console.log("selectedIds", selectedIds);
              // onLineItemDelete(selectedIds)
            }}
            onWishlist={updateAllWishlistItems}
            itemCount={selectedCount}
            selectedIds={lineItemsWithSelected
              ?.filter((item) => item?.isSelected)
              .map((item) => item?.node?.id)}
          />
        )}

        {showWishlistConfirmation && (
          <Confirmations
            onClose={() => setShowWishlistConfirmation(false)}
            onWishlist={() => {
              updateAllWishlistItems();
              const deleteIds = lineItemsWithSelected
                ?.filter((item) => item?.isSelected)
                .map((item) => item?.node?.id);

              // console.log("onWishlist", selectedIds);

              onLineItemDelete(deleteIds);
            }}
            itemCount={selectedCount}
            desc={'You are just a step away from making it yours'}
            title={`Are you sure you want to move ${selectedCount} items to wishlist?`}
            onlyWishlist
            selectedIds={lineItemsWithSelected
              ?.filter((item) => item?.isSelected)
              .map((item) => item?.node?.id)}
          />
        )}
      </ScrollView>

      {loading && (
        <View style={styles.overlay}>
          <ActivityIndicator size="large" color="#fff" />
        </View>
      )}

      {showModal && (
        <ProductModal
          modalVisible={showModal}
          toggleModal={() => {
            setShowModal(!showModal);
          }}
          productVariantDetails={selectedProductData}
          props={props}
          isFromCart={true}
        // onSuccess={(itemsToAdd) => {
        //     manageCart({
        //         lineItemsToAdd: itemsToAdd,
        //         // showMessage: true,
        //         updateCartPageStateRequired: true,
        //     });
        // }}
        />
      )}

      {/* Applied Voucher Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={showAppliedVoucherModal}
        onRequestClose={() => setShowAppliedVoucherModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowAppliedVoucherModal(false)}
            >
              <Svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <Path d="M18 6L6 18" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <Path d="M6 6L18 18" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </Svg>
            </TouchableOpacity>

            <View style={styles.appliedVoucherModalContent}>
              <View style={styles.giftIconContainer}>
                <GiftCardIcon width={32} height={32} />
                <Text style={styles.giftTitle}>
                  {/* If all are same type, show that, else show 'Gift Card / Gift Voucher' */}
                  {appliedGiftItems.length > 0 && appliedGiftItems.every(item => item.type === 'voucher')
                    ? 'Gift Voucher'
                    : appliedGiftItems.length > 0 && appliedGiftItems.every(item => item.type === 'giftcard')
                      ? 'Gift Card'
                      : 'Gift Card / Gift Voucher'}
                </Text>
              </View>

              {/* Show all applied GC/GV */}
              {appliedGiftItems.map((item, idx) => (
                <View key={item.number + idx} style={styles.appliedVoucherRow}>
                  <Text style={styles.voucherNumberText}>{item.number}</Text>
                  <View style={styles.voucherAmountContainer}>
                    <Text style={styles.voucherAmountText}>₹ {item.amount}</Text>
                    <Svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <Path d="M10 18.3333C14.6024 18.3333 18.3334 14.6024 18.3334 10C18.3334 5.39763 14.6024 1.66667 10 1.66667C5.39765 1.66667 1.66669 5.39763 1.66669 10C1.66669 14.6024 5.39765 18.3333 10 18.3333Z" fill="#4CAF50" fillOpacity="0.1" stroke="#4CAF50" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <Path d="M6.66669 10L9.16669 12.5L13.3334 7.5" stroke="#4CAF50" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </Svg>
                  </View>
                  <TouchableOpacity
                    style={styles.removeVoucherButton}
                    disabled={deleteLoading}
                    onPress={async (e) => {
                      e.stopPropagation();
                      if (deleteLoading) return;
                      setDeleteLoading(true);
                      try {
                        // Always try cancel first, and only remove if cancel is successful
                        let cancelSuccess = false;
                        try {
                          const cancelRes = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/cancel', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/" },
                            body: JSON.stringify({ voucher: item.number }),
                          });
                          const cancelData = await cancelRes.json();
                          if (cancelData.Code === '00') {
                            cancelSuccess = true;
                          } else {
                            setVoucherError(cancelData.message || 'Failed to cancel voucher');
                          }
                        } catch (err) {
                          setVoucherError('Failed to cancel voucher. Please try again.');
                        }
                        // Always call refund, but ignore its error for UI
                        try {
                          await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/refund', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/" },
                            body: JSON.stringify({ number: item.number }),
                          });
                        } catch (err) {}
                        // Only remove from UI if cancel was successful
                        if (cancelSuccess) {
                          setAppliedGiftItems(prev => {
                            const updated = prev.filter(v => v.number !== item.number);
                            // If last item, close modal
                            if (updated.length === 0) setShowAppliedVoucherModal(false);
                            return updated;
                          });
                          setVoucherError('');
                        }
                      } finally {
                        setDeleteLoading(false);
                      }
                    }}
                  >
                    <Svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <Path d="M2.5 5H4.16667H17.5" stroke="#221F20" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <Path d="M15.8333 5.00001V16.6667C15.8333 17.1087 15.6577 17.5326 15.3452 17.8452C15.0326 18.1577 14.6087 18.3333 14.1666 18.3333H5.83329C5.39127 18.3333 4.96734 18.1577 4.65478 17.8452C4.34222 17.5326 4.16663 17.1087 4.16663 16.6667V5.00001M6.66663 5.00001V3.33334C6.66663 2.89131 6.84222 2.46739 7.15478 2.15483C7.46734 1.84227 7.89127 1.66667 8.33329 1.66667H11.6666C12.1087 1.66667 12.5326 1.84227 12.8452 2.15483C13.1577 2.46739 13.3333 2.89131 13.3333 3.33334V5.00001" stroke="#221F20" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </Svg>
                  </TouchableOpacity>
                </View>
              ))}

              <View style={styles.buttonRow}>
                <TouchableOpacity
                  style={styles.addVoucherButton}
                  onPress={() => {
                    setShowAppliedVoucherModal(false);
                    // If all are vouchers, open voucher tab, else open card tab
                    if (appliedGiftItems.length > 0 && appliedGiftItems.every(item => item.type === 'voucher')) {
                      setActiveTab('Gift Voucher');
                    } else {
                      setActiveTab('Gift Card');
                    }
                    setGiftCardNumber('');
                    setGiftCardPin('');
                    setGiftVoucherNumber('');
                    setBalanceToUse('');
                    setVoucherError('');
                    setGiftCardDetails(null);
                    setShowGiftModal(true);
                    generateCaptcha();
                  }}
                >
                  <Text style={styles.addVoucherButtonText}>
                    {appliedGiftItems.length > 0 && appliedGiftItems.every(item => item.type === 'voucher') ? '+ ADD GIFT VOUCHER' : '+ ADD GIFT CARD'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.applyVoucherButton,
                    voucherLoading && styles.disabledButton
                  ]}
                  disabled={voucherLoading}
                  onPress={async () => {
                    setVoucherLoading(true);
                    const userData = getUser();
                    const mobileNumber = userData?.phone || '';
                    const cartValue = cart?.totalPrice?.amount || '0';

                    console.log("mobileNumber", mobileNumber)
                    console.log("cartValue", cartValue)

                    try {
                      // Add a delay to ensure the apply operation has been fully processed
                      console.log("Waiting before redeem to ensure apply operation is processed");
                      await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds
                      console.log("Now proceeding with redeem");

                      // Directly redeem (gift cards should already be applied from the form)
                      const redeemPayload = {
                        mobilenumber: mobileNumber,
                        cartvalue: cartValue
                      };
                      console.log("Redeem Payload:", redeemPayload);

                      const response = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/redeem', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/" },
                        body: JSON.stringify(redeemPayload),
                      });
                      const data = await response.json();
                      console.log("Redeem Response:", data);
                      if (data.success && Array.isArray(data.success) && data.success.length > 0 && data.success[0].Code === "00") {
                        // Fetch the latest applied items from backend
                        await fetchAppliedGiftItems();
                        setShowAppliedVoucherModal(false);
                        console.log("callingRedeemAfterApply")
                        fetchRedeemedAmountSum();
                        fetchRedeemedGiftItems();

                      } else {
                        // Handle specific error cases
                        if (data.error && Array.isArray(data.error) && data.error.length > 0) {
                          const firstError = data.error[0];
                          if (firstError.error === "Please try again after some time.") {
                            setVoucherError('Gift card is being processed. Please wait a moment and try again.');
                          } else {
                            setVoucherError(firstError.error || 'Failed to redeem. Please try again.');
                          }
                        } else {
                          setVoucherError('Failed to redeem. Please try again.');
                        }
                      }
                    } catch (error) {
                      console.error('Error in apply/redeem process:', error);
                      setVoucherError(error.message || 'Failed to process. Please try again.');
                    } finally {
                      setVoucherLoading(false);
                    }
                  }}
                >
                  <Text style={styles.applyButtonText}>
                    {voucherLoading ? '...' : 'APPLY'}
                  </Text>
                </TouchableOpacity>
              </View>

              {voucherError ? (
                <Text style={styles.errorText}>{voucherError}</Text>
              ) : null}

              <View style={styles.infoContainer}>
                <Svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <Path d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z" stroke="#221F20" strokeLinecap="round" strokeLinejoin="round"/>
                  <Path d="M7.5 7.5H8V11H8.5" stroke="#221F20" strokeLinecap="round" strokeLinejoin="round"/>
                  <Path d="M7.875 6C8.28921 6 8.625 5.66421 8.625 5.25C8.625 4.83579 8.28921 4.5 7.875 4.5C7.46079 4.5 7.125 4.83579 7.125 5.25C7.125 5.66421 7.46079 6 7.875 6Z" fill="#221F20"/>
                </Svg>
                <Text style={styles.infoText}>
                  All Valid {appliedGiftItems.length > 0 && appliedGiftItems.every(item => item.type === 'voucher') ? 'Gift Vouchers' : 'Gift Cards'} will be added to the Cart
                </Text>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* Gift Card/Voucher Modal */}
      <Modal
animationType="slide"
transparent={true}
visible={showGiftModal}
onRequestClose={() => setShowGiftModal(false)}
>
<KeyboardAvoidingView
  behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
  style={styles.modalOverlay}
>
  <View style={styles.modalContainer}>
  <TouchableOpacity
      style={styles.closeButton}
      onPress={() => {
        setShowGiftModal(false);
        // Clear any existing timeout
        if (captchaTimeoutRef.current) {
          clearTimeout(captchaTimeoutRef.current);
          captchaTimeoutRef.current = null;
        }
        // Reset captcha state
        setCaptchaValue('');
        setCaptchaVerified(false);
        setCaptchaMessage('');
        // Clear auto-fetched details
        setGiftCardDetails(null);
      }}
    >
      <Svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <Path d="M18 6L6 18" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <Path d="M6 6L18 18" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      </Svg>
    </TouchableOpacity>
  <ScrollView
      contentContainerStyle={{ flexGrow: 1 }}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
    >
   

    {/* Tab Buttons */}
    <View style={styles.tabContainer}>
      <TouchableOpacity
        style={[
          styles.tabButton,
          activeTab === 'Gift Card' ? styles.activeTabButton : {},
          (appliedGiftItems.some(item => item.type === 'voucher')) ? styles.disabledTabButton : {}
        ]}
        onPress={() => {
          if (!appliedGiftItems.some(item => item.type === 'voucher')) {
            setActiveTab('Gift Card');
            setGiftCardNumber('');
            setGiftCardPin('');
            setGiftVoucherNumber('');
            setBalanceToUse('');
            setVoucherError('');
            setGiftCardDetails(null);
            generateCaptcha();
          }
        }}
        disabled={appliedGiftItems.some(item => item.type === 'voucher')}
      >
        <Text style={[
          styles.tabButtonText,
          activeTab === 'Gift Card' ? styles.activeTabButtonText : {},
          (appliedGiftItems.some(item => item.type === 'voucher')) ? styles.disabledTabButtonText : {}
        ]}>Gift Card</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.tabButton,
          activeTab === 'Gift Voucher' ? styles.activeTabButton : {},
          (appliedGiftItems.some(item => item.type === 'giftcard')) ? styles.disabledTabButton : {}
        ]}
        onPress={() => {
          if (!appliedGiftItems.some(item => item.type === 'giftcard')) {
            setActiveTab('Gift Voucher');
            setGiftCardNumber('');
            setGiftCardPin('');
            setGiftVoucherNumber('');
            setBalanceToUse('');
            setVoucherError('');
            setGiftCardDetails(null);
            generateCaptcha();
          }
        }}
        disabled={appliedGiftItems.some(item => item.type === 'giftcard')}
      >
        <Text style={[
          styles.tabButtonText,
          activeTab === 'Gift Voucher' ? styles.activeTabButtonText : {},
          (appliedGiftItems.some(item => item.type === 'giftcard')) ? styles.disabledTabButtonText : {}
        ]}>Gift Voucher</Text>
      </TouchableOpacity>
    </View>

    {/* Gift Card Icon */}
    <View style={styles.giftIconContainer}>
      <GiftCardIcon width={32} height={32} />
      <Text style={styles.giftTitle}>
        {activeTab === 'Gift Card' ? 'Gift Card' : 'Gift Voucher'}
      </Text>
    </View>

    {/* Gift Card Form */}
    {activeTab === 'Gift Card' ? (
      <View style={styles.formContainer}>
        <TextInput
          style={styles.input}
          placeholder="Gift Card No."
          value={giftCardNumber}
          onChangeText={setGiftCardNumber}
          maxLength={20}
        />
        <TextInput
          style={styles.input}
          placeholder="PIN"
          value={giftCardPin}
          onChangeText={setGiftCardPin}
          secureTextEntry
        />
        <View style={styles.captchaOuterContainer}>
          <View style={styles.captchaContainer}>
            <View style={styles.captchaBox}>
              <Text style={styles.captchaText}>{captchaText}</Text>
              <TouchableOpacity
                style={styles.refreshButton}
                onPress={generateCaptcha}
              >
                <Svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <Path d="M15.8333 4.16667V8.33334H11.6667" stroke="#221F20" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <Path d="M4.16669 15.8333V11.6667H8.33335" stroke="#221F20" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <Path d="M6.05835 7.50001C6.38614 6.77432 6.8739 6.13864 7.48527 5.64613C8.09664 5.15361 8.81403 4.82064 9.57539 4.67621C10.3367 4.53178 11.1182 4.58077 11.8548 4.81892C12.5915 5.05707 13.2614 5.47673 13.8 6.04168L15.8334 8.33334M4.16669 11.6667L6.20002 13.9583C6.73863 14.5233 7.40853 14.9429 8.14515 15.1811C8.88177 15.4192 9.66328 15.4682 10.4246 15.3238C11.186 15.1794 11.9034 14.8464 12.5148 14.3539C13.1261 13.8614 13.6139 13.2257 13.9417 12.5" stroke="#221F20" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </Svg>
              </TouchableOpacity>
            </View>
            <View style={styles.captchaInputContainer}>
              <TextInput
                style={styles.captchaInput}
                placeholder="Enter Captcha"
                value={captchaValue}
                onChangeText={(text) => {
                  setCaptchaValue(text);
                  verifyCaptcha(text);
                }}
              />
              {captchaMessage ? (
                <Text
                  style={[
                    styles.captchaMessage,
                    captchaVerified ? styles.captchaSuccess : styles.captchaError
                  ]}
                >
                  {captchaMessage}
                </Text>
              ) : null}
            </View>
          </View>
        </View>

        {/* Auto-fetched Gift Card Details */}
        {giftCardDetailsLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#221F20" />
            <Text style={styles.loadingText}>Fetching card details...</Text>
          </View>
        ) : giftCardDetails ? (
          <View style={styles.giftCardDetailsContainer}>
            <View style={styles.balanceDisplayContainer}>
              <Text style={styles.balanceDisplayLabel}>Current Card Balance</Text>
              <Text style={styles.balanceDisplayValue}>₹{giftCardDetails.totalavailablepoints}</Text>
            </View>
            <TextInput
              style={[
                styles.balanceInput,
                giftCardDetails?.provider?.toLowerCase() === 'razorpay' && styles.disabledInput
              ]}
              placeholder="Balance To Use"
              value={balanceToUse}
              onChangeText={giftCardDetails?.provider?.toLowerCase() === 'razorpay' ? undefined : setBalanceToUse}
              keyboardType="numeric"
              editable={giftCardDetails?.provider?.toLowerCase() !== 'razorpay'}
            />
          </View>
        ) : (
          <View style={styles.balanceContainer}>
            <Text style={styles.balanceLabel}>Current Card Balance ₹</Text>
            <TextInput
              style={styles.balanceInput}
              placeholder="Balance To Use"
              value={balanceToUse}
              onChangeText={setBalanceToUse}
              keyboardType="numeric"
            />
          </View>
        )}
        <TouchableOpacity
          style={[
            styles.applyButton,
            (!captchaVerified || voucherLoading || !giftCardNumber || !giftCardPin || (giftCardDetails && !balanceToUse)) && styles.disabledButton
          ]}
          disabled={!captchaVerified || voucherLoading || !giftCardNumber || !giftCardPin || (giftCardDetails && !balanceToUse)}
          onPress={async () => {
            if (giftCardDetails && balanceToUse) {
              // If details are already fetched and user has entered amount, apply the gift card
              await applyGiftCardWithAmount();
            } else {
              // If details are not fetched, fetch them first
              checkGiftCardDetails(giftCardNumber, giftCardPin);
            }
          }}
        >
          <Text style={styles.applyButtonText}>
            {voucherLoading ? 'PROCESSING...' : 'APPLY'}
          </Text>
        </TouchableOpacity>
        {voucherError ? (
          <Text style={styles.errorText}>{voucherError}</Text>
        ) : null}
        <TouchableOpacity
          style={styles.termsButton}
          onPress={() => {
            handleAction({
              action: 'OPEN_WEBVIEW',
              params: {
                url: 'https://jockey-uat.myshopify.com/pages/terms-of-gift-cards-and-vouchers',
                title: 'Terms of Gift Card & Gift Voucher',
                replacePage: false,
              },
            });
          }}
        >
          <Svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M8.00004 14.6667C11.6819 14.6667 14.6667 11.6819 14.6667 8.00004C14.6667 4.31814 11.6819 1.33337 8.00004 1.33337C4.31814 1.33337 1.33337 4.31814 1.33337 8.00004C1.33337 11.6819 4.31814 14.6667 8.00004 14.6667Z" stroke="#221F20" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M8 5.33337V8.00004" stroke="#221F20" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M8 10.6666H8.00667" stroke="#221F20" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
          </Svg>
          <Text style={styles.termsText}>Terms of Gift Card & Gift Voucher</Text>
        </TouchableOpacity>
      </View>
    ) : (
      <View style={styles.formContainer}>
        <TextInput
          style={styles.input}
          placeholder="Gift Voucher No"
          value={giftVoucherNumber}
          onChangeText={setGiftVoucherNumber}
          maxLength={50}
        />
        <View style={styles.captchaOuterContainer}>
          <View style={styles.captchaContainer}>
            <View style={styles.captchaBox}>
              <Text style={styles.captchaText}>{captchaText}</Text>
              <TouchableOpacity
                style={styles.refreshButton}
                onPress={generateCaptcha}
              >
                <Svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <Path d="M15.8333 4.16667V8.33334H11.6667" stroke="#221F20" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <Path d="M4.16669 15.8333V11.6667H8.33335" stroke="#221F20" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <Path d="M6.05835 7.50001C6.38614 6.77432 6.8739 6.13864 7.48527 5.64613C8.09664 5.15361 8.81403 4.82064 9.57539 4.67621C10.3367 4.53178 11.1182 4.58077 11.8548 4.81892C12.5915 5.05707 13.2614 5.47673 13.8 6.04168L15.8334 8.33334M4.16669 11.6667L6.20002 13.9583C6.73863 14.5233 7.40853 14.9429 8.14515 15.1811C8.88177 15.4192 9.66328 15.4682 10.4246 15.3238C11.186 15.1794 11.9034 14.8464 12.5148 14.3539C13.1261 13.8614 13.6139 13.2257 13.9417 12.5" stroke="#221F20" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </Svg>
              </TouchableOpacity>
            </View>
            <View style={styles.captchaInputContainer}>
              <TextInput
                style={styles.captchaInput}
                placeholder="Enter captcha"
                value={captchaValue}
                onChangeText={(text) => {
                  setCaptchaValue(text);
                  verifyCaptcha(text);
                }}
              />
              {captchaMessage ? (
                <Text
                  style={[
                    styles.captchaMessage,
                    captchaVerified ? styles.captchaSuccess : styles.captchaError
                  ]}
                >
                  {captchaMessage}
                </Text>
              ) : null}
            </View>
          </View>
        </View>
        <TouchableOpacity
          style={[
            styles.applyButton,
            (!captchaVerified || voucherLoading) && styles.disabledButton
          ]}
          disabled={!captchaVerified || voucherLoading}
          onPress={() => checkVoucherDetails(giftVoucherNumber)}
        >
          <Text style={styles.applyButtonText}>
            {voucherLoading ? 'PROCESSING...' : 'APPLY'}
          </Text>
        </TouchableOpacity>
        {voucherError ? (
          <Text style={styles.errorText}>{voucherError}</Text>
        ) : null}
        <TouchableOpacity
          style={styles.termsButton}
          onPress={() => {
            handleAction({
              action: 'OPEN_WEBVIEW',
              params: {
                url: 'https://jockey-uat.myshopify.com/pages/terms-of-gift-cards-and-vouchers',
                title: 'Terms of Gift Card & Gift Voucher',
                replacePage: false,
              },
            });
          }}
        >
          <Svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <Path d="M8.00004 14.6667C11.6819 14.6667 14.6667 11.6819 14.6667 8.00004C14.6667 4.31814 11.6819 1.33337 8.00004 1.33337C4.31814 1.33337 1.33337 4.31814 1.33337 8.00004C1.33337 11.6819 4.31814 14.6667 8.00004 14.6667Z" stroke="#221F20" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M8 5.33337V8.00004" stroke="#221F20" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M8 10.6666H8.00667" stroke="#221F20" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
          </Svg>
          <Text style={styles.termsText}>Terms of Gift Card & Gift Voucher</Text>
        </TouchableOpacity>
      </View>
    )}
    </ScrollView>
  </View>
</KeyboardAvoidingView>
</Modal> 
      {/* Error popup for redeem amount */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={!!redeemAmountError}
        onRequestClose={() => setRedeemAmountError('')}
      >
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.4)' }}>
          <View style={{ backgroundColor: '#fff', padding: 24, borderRadius: 8, alignItems: 'center', maxWidth: '80%' }}>
            <Text style={{ color: '#F44336', fontSize: 16, fontWeight: 'bold', marginBottom: 12, textAlign: 'center' }}>{redeemAmountError}</Text>
            <TouchableOpacity onPress={() => setRedeemAmountError('')} style={{ marginTop: 8, backgroundColor: '#221f20', borderRadius: 4, paddingVertical: 8, paddingHorizontal: 24 }}>
              <Text style={{ color: '#fff', fontWeight: '600' }}>OK</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </>
  );
}; 

export default CustomCartBlock;

const getLocalStyles = () => {
  return StyleSheet.create({
    overlay: {
      ...StyleSheet.absoluteFillObject, // Covers the entire screen
      backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black background
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1,
    },
    wishlistIcon: {
      position: 'absolute',
      top: 11,
      right: 11,
      width: widthPixel(26), // Size to create the circle
      height: widthPixel(26),
      borderRadius: widthPixel(13), // Half of the width/height to make it circular
      justifyContent: 'center',
      alignItems: 'center',
    },
    productTitle: {
      fontSize: fonts._13,
      fontFamily: fonts.FONT_FAMILY.SemiBold,
    },
    youMayLikeText: {
      fontSize: fonts._16,
      letterSpacing: 0.5,
      fontFamily: fonts.FONT_FAMILY.Medium,
      color: '#221f20',
      paddingTop: heightPixel(16),
    },
    rowContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 2,
    },
    separator: {
      width: 1,
      backgroundColor: 'rgba(34,31,32,.17)',
      marginHorizontal: widthPixel(5),
    },
    selectedText: {
      fontFamily: 'Jost-Regular',
      fontSize: fonts._14,
      paddingLeft: widthPixel(3),
    },
    rowEndContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      flex: 1,
    },
    multiSelectContainer: {
      paddingHorizontal: widthPixel(11),
      paddingLeft: widthPixel(11),
      backgroundColor: '#fafafa',
      paddingVertical: heightPixel(8),
      marginVertical: heightPixel(8),
      borderRadius: widthPixel(4),
      flexDirection: 'row',
      alignItems: 'center',
      // justifyContent: 'center'
    },
    itemInCartText: {
      fontSize: fonts._16,
      fontFamily: fonts.FONT_FAMILY.SemiBold,
      color: '#221f20',
    },
    headerContainer: {
      backgroundColor: '#fff',
      paddingHorizontal: widthPixel(24),
      paddingTop: heightPixel(16),
      paddingBottom: heightPixel(8),
    },
    mainContainer: {
      backgroundColor: '#fff',
      paddingHorizontal: widthPixel(16),
      paddingBottom: heightPixel(16),
    },
    scrollView: {
      height: '100%',
    },
    cardContainer: {
      // width: width * 0.473,
      width: width / 2,
      borderWidth: 0,
      backgroundColor: 'rgb(254,254,255)',
      borderRadius: widthPixel(13),
      marginHorizontal: 5,
      marginVertical: heightPixel(10),
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      overflow: 'hidden',
      borderWidth: 0.5,
      borderColor: 'rgba(34,31,32,.1)',
    },
    imageContainer: {
      height: heightPixel(220), // Image container takes 70% of the card's height
      borderWidth: 0,
      paddingBottom: 0,
      position: 'relative',
      padding: widthPixel(4),
    },
    productPrice: {
      fontFamily: fonts.FONT_FAMILY.SemiBold,
      fontSize: fonts._13,
      lineHeight: heightPixel(20),
      marginTop: heightPixel(4),
      paddingLeft: widthPixel(10),
    },
    cartIcon: {
      width: width * 0.117,
      height: height * 0.048,
      borderTopStartRadius: 10,
      borderBottomEndRadius: 10,
    },
    tagContainer: {
      marginVertical: heightPixel(12),
      paddingHorizontal: widthPixel(8),
    },
    image: {
      width: '100%',
      height: '100%',
      borderRadius: widthPixel(12),
    },
    bottomContainer: {
      width: '100%',
      paddingTop: heightPixel(10),
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    // Gift Card/Voucher styles
    giftCardContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#fff',
      borderWidth: 1,
      borderColor: '#E5E5E5',
      borderRadius: widthPixel(8),
      padding: widthPixel(16),
      marginVertical: heightPixel(12),
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: widthPixel(2),
      elevation: 2,
    },
    giftCardIconContainer: {
      marginRight: widthPixel(12),
    },
    giftCardText: {
      flex: 1,
      fontSize: fonts._14,
      fontFamily: fonts.FONT_FAMILY.Medium,
      color: '#221f20',
    },
    addButton: {
      backgroundColor: '#221f20',
      paddingVertical: heightPixel(8),
      paddingHorizontal: widthPixel(20),
      borderRadius: widthPixel(5),
    },
    addButtonText: {
      color: '#fff',
      fontSize: fonts._12,
      fontFamily: fonts.FONT_FAMILY.Medium,
      fontWeight: '600',
    },
    // Modal styles
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContainer: {
      width: '90%',
      backgroundColor: '#fff',
      borderRadius: widthPixel(12),
      padding: widthPixel(24),
      maxHeight: '80%',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: widthPixel(4),
      elevation: 5,
    },
    closeButton: {
      position: 'absolute',
      top: widthPixel(10),
      right: widthPixel(16),
      zIndex: 1,
      padding: widthPixel(2),
    },
    tabContainer: {
      flexDirection: 'row',
      marginTop: heightPixel(8),
      marginBottom: heightPixel(24),
      borderBottomWidth: 1,
      borderBottomColor: '#E5E5E5',
    },
    tabButton: {
      flex: 1,
      paddingVertical: heightPixel(12),
      alignItems: 'center',
      borderBottomWidth: 2,
      borderBottomColor: 'transparent',
      marginHorizontal: widthPixel(4),
    },
    activeTabButton: {
      borderBottomColor: '#221f20',
    },
    tabButtonText: {
      fontSize: fonts._15,
      fontFamily: fonts.FONT_FAMILY.Regular,
      color: '#787777',
    },
    activeTabButtonText: {
      fontFamily: fonts.FONT_FAMILY.Medium,
      color: '#221f20',
      fontWeight: '500',
    },
    giftIconContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: heightPixel(24),
    },
    giftTitle: {
      fontSize: fonts._18,
      fontFamily: fonts.FONT_FAMILY.Medium,
      color: '#221f20',
      marginLeft: widthPixel(12),
    },
    formContainer: {
      width: '100%',
    },
    input: {
      borderWidth: 1,
      borderColor: '#E5E5E5',
      borderRadius: widthPixel(8),
      padding: widthPixel(14),
      marginBottom: heightPixel(16),
      fontSize: fonts._14,
      fontFamily: fonts.FONT_FAMILY.Regular,
      backgroundColor: '#fff',
    },
    captchaOuterContainer: {
      marginBottom: heightPixel(16),
    },
    captchaContainer: {
      flexDirection: 'row',
      gap: widthPixel(8),
      minHeight: heightPixel(52), // Ensure minimum height even when message appears
    },
    captchaBox: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderWidth: 1,
      borderColor: '#E5E5E5',
      borderRadius: widthPixel(8),
      padding: widthPixel(14),
      backgroundColor: '#f5f5f5',
      height: heightPixel(52), // Fixed height to match input
    },
    captchaText: {
      fontSize: fonts._14,
      fontFamily: fonts.FONT_FAMILY.Medium,
      color: '#221f20',
      letterSpacing: 1,
    },
    refreshButton: {
      padding: widthPixel(4),
    },
    captchaInputContainer: {
      flex: 1,
      flexDirection: 'column',
      minHeight: heightPixel(52), // Ensure minimum height even when message appears
    },
    captchaInput: {
      borderWidth: 1,
      borderColor: '#E5E5E5',
      borderRadius: widthPixel(8),
      padding: widthPixel(14),
      fontSize: fonts._14,
      fontFamily: fonts.FONT_FAMILY.Regular,
      height: heightPixel(52), // Fixed height to match captcha box
    },
    balanceContainer: {
      marginBottom: heightPixel(16),
    },
    balanceLabel: {
      fontSize: fonts._14,
      fontFamily: fonts.FONT_FAMILY.Regular,
      color: '#221f20',
      marginBottom: heightPixel(8),
    },
    balanceInput: {
      borderWidth: 1,
      borderColor: '#E5E5E5',
      borderRadius: widthPixel(8),
      padding: widthPixel(14),
      fontSize: fonts._14,
      fontFamily: fonts.FONT_FAMILY.Regular,
    },
    disabledInput: {
      backgroundColor: '#f5f5f5',
      color: '#999999',
    },
    applyButton: {
      backgroundColor: '#221f20',
      borderRadius: widthPixel(4),
      paddingVertical: heightPixel(14),
      alignItems: 'center',
      marginBottom: heightPixel(16),
      marginTop: heightPixel(8),
    },
    applyButtonText: {
      fontSize: fonts._14,
      fontFamily: fonts.FONT_FAMILY.Medium,
      color: '#fff',
      fontWeight: '600',
      letterSpacing: 1,
    },
    termsButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: heightPixel(8),
    },
    termsText: {
      fontSize: fonts._12,
      fontFamily: fonts.FONT_FAMILY.Regular,
      color: '#221f20',
      marginLeft: widthPixel(8),
    },
    captchaMessage: {
      fontSize: fonts._12,
      fontFamily: fonts.FONT_FAMILY.Medium,
      textAlign: 'center',
      marginTop: heightPixel(4),
      paddingLeft: widthPixel(4),
    },
    captchaSuccess: {
      color: '#4CAF50',
    },
    captchaError: {
      color: '#F44336',
    },
    disabledButton: {
      backgroundColor: '#CCCCCC',
      opacity: 0.7,
    },
    errorText: {
      color: '#F44336',
      fontSize: fonts._12,
      fontFamily: fonts.FONT_FAMILY.Medium,
      textAlign: 'center',
      marginBottom: heightPixel(8),
    },
    appliedVoucherContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#FFFFFF',
      borderRadius: widthPixel(8),
      padding: widthPixel(10),
      marginVertical: heightPixel(2),
      justifyContent: 'space-between',
      borderWidth: 1,
      borderColor: '#E5E5E5',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    voucherInfoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,

    },
    voucherDetailsContainer: {
      marginLeft: widthPixel(12),
      flex: 1,
    },
    voucherTypeText: {
      fontSize: fonts._12,
      fontFamily: fonts.FONT_FAMILY.Regular,
      color: '#666666',
      marginBottom: heightPixel(2),
    },
    voucherNumberText: {
      fontSize: fonts._14,
      fontFamily: fonts.FONT_FAMILY.Medium,
      color: '#221f20',
    },
    voucherRightContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    voucherAmountText: {
      fontSize: fonts._16,
      fontFamily: fonts.FONT_FAMILY.SemiBold,
      color: '#221f20',
      marginRight: widthPixel(8),
    },
    removeVoucherButton: {
      padding: widthPixel(8),
      borderRadius: widthPixel(4),
      backgroundColor: '#F5F5F5',
    },
    disabledTabButton: {
      opacity: 0.5,
      backgroundColor: '#F5F5F5',
    },
    disabledTabButtonText: {
      color: '#AAAAAA',
    },
    appliedVoucherModalContent: {
      width: '100%',
      paddingTop: heightPixel(8),
    },
    appliedVoucherRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#F5F5F5',
      borderRadius: widthPixel(8),
      padding: widthPixel(10),
      marginVertical: heightPixel(6),
      borderWidth: 1,
      borderColor: '#E5E5E5',
    },
    voucherAmountContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: widthPixel(4),
    },
    trashButton: {
      padding: widthPixel(4),
    },
    buttonRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: widthPixel(12),
      marginBottom: heightPixel(16),
    },
    addVoucherButton: {
      flex: 2,
      borderWidth: 1,
      borderColor: '#221f20',
      borderRadius: widthPixel(4),
      paddingVertical: heightPixel(14),
      alignItems: 'center',
    },
    applyVoucherButton: {
      flex: 1,
      backgroundColor: '#221f20',
      borderRadius: widthPixel(4),
      paddingVertical: heightPixel(14),
      alignItems: 'center',
    },
    addVoucherButtonText: {
      fontSize: fonts._12,
      fontFamily: fonts.FONT_FAMILY.Medium,
      color: '#221f20',
      fontWeight: '600',
      letterSpacing: 1,
    },
    infoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: heightPixel(8),
    },
    infoText: {
      fontSize: fonts._12,
      fontFamily: fonts.FONT_FAMILY.Regular,
      color: '#221f20',
      marginLeft: widthPixel(8),
    },
    // Auto-fetch gift card details styles
    loadingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: heightPixel(16),
      marginBottom: heightPixel(16),
    },
    loadingText: {
      fontSize: fonts._14,
      fontFamily: fonts.FONT_FAMILY.Regular,
      color: '#221f20',
      marginLeft: widthPixel(8),
    },
    giftCardDetailsContainer: {
      marginBottom: heightPixel(16),
    },
    balanceDisplayContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: heightPixel(12),
      paddingVertical: heightPixel(8),
      paddingHorizontal: widthPixel(12),
      backgroundColor: '#fff',
      borderRadius: widthPixel(6),
      borderWidth: 1,
      borderColor: '#E5E5E5',
    },
    balanceDisplayLabel: {
      fontSize: fonts._14,
      fontFamily: fonts.FONT_FAMILY.Regular,
      color: '#221f20',
    },
    balanceDisplayValue: {
      fontSize: fonts._16,
      fontFamily: fonts.FONT_FAMILY.SemiBold,
      color: '#221f20',
    },
  });
};
