import AsyncStorage from '@react-native-async-storage/async-storage';
import { runDataSource } from '@appmaker-xyz/core';
import { getSettings } from '../../config';
import Snackbar from 'react-native-snackbar';

const dataSource = {
    attributes: {},
    source: "shopify",
};
// import NetInfo from "@react-native-community/netinfo";

export const getRecentSearches = async () => new Promise(async resolve => {
    try {
        const searches = await AsyncStorage.getItem(
            'searches',
        );
        const searchesJson = JSON.parse(searches);
        resolve(searchesJson);
    } catch (error) {
        resolve(null);
    }
});
export const setRecentSearches = async (value) => {
    try {
        const jsonValue = JSON.stringify(value);
        await AsyncStorage.setItem('searches', jsonValue);
    } catch (e) {
        // saving error
    }
};

export const showSnackbarWithIcon = (text) => {
    Snackbar.show({
        // text: 'The product is in your bag.',
        text: text,
        duration: Snackbar.LENGTH_SHORT,
        backgroundColor: 'black',
        action: {
            title: 'Dismiss',
            color: 'white',
            onPress: () => {
                Snackbar.dismiss();
            },
        },
        customStyle: {
            flexDirection: 'row',
            alignItems: 'center',
            paddingLeft: 10,
        },
    });
};

export const getQueryParamsFromLocal = async () => {
    try {
        const jsonValue = await AsyncStorage.getItem('query_params');

        console.log("jsonValue::::", jsonValue);

        if (jsonValue != null) {
            const data = JSON.parse(jsonValue);
            console.log("currentTime > data.expiry:::::", currentTime > data.expiry);
            const currentTime = Date.now();
            if (currentTime > data.expiry) {
                console.log("kkkkkkkkk");

                await AsyncStorage.removeItem('query_params');
                return null;
            }
            return data;
        }
        return null; // If no data found
    } catch (e) {
        // Handle retrieval error
        console.error('Error retrieving data with expiry:', e);
        return null;
    }
};


export const storeWithExpiry = async (key, value) => {
    try {
        await AsyncStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
        console.error('Error storing data with expiry:', error);
    }
};

export const getWithExpiry = async (key) => {

    try {
        const storedData = await AsyncStorage.getItem(key);
        if (storedData) {
            const parsedData = JSON.parse(storedData);
            let today = new Date();
            let formattedDate = today.toISOString().split('T')[0];
            const expiryDate = parsedData?.expiryDate;
            const expiryDateObj = new Date(expiryDate);
            const formattedDateObj = new Date(formattedDate);

            if (expiryDateObj >= formattedDateObj) {
                console.log("Data is valid, returning value");
                return parsedData.value;
            } else {
                await AsyncStorage.removeItem(key);
                console.log('The expiry date is not greater than today\'s date.');
                return {};
            }
        } else {
            console.log("No data found");
            return {};
        }
    } catch (error) {
        console.error('Error retrieving data with expiry:', error);
        return {};
    }
};

export const extractH3Tags = (htmlString) => {
    // Regular expression to match <h3> tags and extract their inner text
    const h3Regex = /<h3>(.*?)<\/h3>/g;
    let matches;
    let h3Tags = [];

    // Use regex to find all <h3> matches
    while ((matches = h3Regex.exec(htmlString)) !== null) {
        h3Tags.push(matches[1]); // matches[1] contains the inner text of the <h3> tag
    }

    // Return the first 3 <h3> tags (if there are fewer, it will return whatever is available)
    return h3Tags.slice(0, 3);
};


export const toSentenceCase = (str) => {
    if (!str) return;
    return str?.charAt(0)?.toUpperCase() + str?.slice(1).toLowerCase();
}

export const getIdFromGID = (id) => {
    let parts = id?.split('/');
    return parts?.pop();
}
export const isEmpty = (obj) => Object.keys(obj).length === 0;


export const getAutoLoginMyProfileRes = async (customerId, navUrl) => {
    const settings = getSettings();
    const response = await fetch(
        `${settings.onboard_base_url}/api/multipass/generate`,
        {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'version': '1' },
            body: JSON.stringify({
                "customerId": customerId,
                "navUrl": navUrl
            }),
        },
    );

    const data = await response.json();

    return data?.multipassResponse;
}

export const getItemsWithIds = async (data) => {
    const tempData = [];
    const promises = data?.map(async (item) => {
        const product = item;
        const handleAttribute = product?.attributes?.find(attr => attr?.name === "Handle");
        const handleValue = handleAttribute ? handleAttribute?.values?.[0]?.value?.[0] : null;

        const [response] = await runDataSource(
            {
                dataSource,
            },
            {
                methodName: "gqlQuery",
                params: {
                    query: `
      {
  productByHandle(handle: "${handleValue}") {
  id
        color_variant: metafield(key: "color_variant", namespace: "custom") {
        value
    }
  }
  } `,
                },
            }
        );

        tempData.push({ ...item, mainId: response?.data?.data?.productByHandle?.id });
    });

    // Wait for all async operations to finish
    await Promise.all(promises);

    return tempData;
};

export const updateWebWishlist = async (customerId, productid, variantid, action = "add") => {
    const settings = getSettings();
    const formData = new FormData();
    formData.append('productid', productid);
    formData.append('variantid', variantid);
    formData.append('customerid', customerId?.toString());
    formData.append('action', action);
    formData.append('apikey', settings.wishlist_api_key);
    formData.append('version', "1");

    const requestOptions = {
        method: "POST",
        body: formData,
    };

    const response = await fetch(`${settings.wishlist_api_url}/a/wishlist?type=api`, requestOptions);
    // console.log("response.json:::", await response.json());


}

export const extractBasketId = (url) => {
    const regex = /Checkout\/([a-f0-9]{32})/;
    const match = url.match(regex);
    return match ? match[1] : null;
}

export const extractQueryParamsFromURL = (url) => {
    try {

        const formattedUrl = url.startsWith('jockey-in://')
            ? 'https://example.com?' + url.split('?')[1]  // Replace with a base URL and retain query part
            : url; // For regular HTTP/HTTPS URLs, no modification needed

        // Extract the query string after the '?' manually
        const queryString = formattedUrl.split('?')[1];
        const queryParams = {};

        if (queryString) {
            try {
                // Using URLSearchParams to parse query string if it's supported
                const params = new URLSearchParams(queryString);

                // If URLSearchParams works (it should), populate the queryParams object
                if (params && typeof params.forEach === 'function') {
                    params.forEach((value, key) => {
                        queryParams[key] = value;
                    });
                } else {
                    // If URLSearchParams isn't available, manually parse query string
                    queryString.split('&').forEach(pair => {
                        const [key, value] = pair.split('=');
                        if (key) {
                            queryParams[key] = decodeURIComponent(value || '');
                        }
                    });
                }
            } catch (error) {
                console.error("Error parsing query parameters:", error);
            }
            console.log("queryParams", queryParams);


        }

        let today = new Date();
        let formattedDate = today.toISOString().split('T')[0];

        let expiryTime = today;

        if ('utm_campaign' in queryParams && 'utm_medium' in queryParams && 'utm_source' in queryParams) {
            if (queryParams?.utm_source === "optimisemedia") {
                expiryTime = new Date(today);
                expiryTime.setDate(today.getDate() + 30);
            } else {
                expiryTime = new Date(today);
                expiryTime.setDate(today.getDate() + 1);
            }
        }

        if ('utm_source_direct' in queryParams && 'utm_medium_direct' in queryParams && 'utm_campaign_direct' in queryParams) {
            console.log("7");
            expiryTime = new Date(today);
            expiryTime.setDate(today.getDate() + 7);
        }


        let paramWithExpiry = {

            expiryDate: expiryTime.toISOString().split('T')[0],
            value: queryParams

        }

        storeWithExpiry("query_params", paramWithExpiry);


    }
    catch (error) {

        console.log("error", error);
    } finally {

    }

}